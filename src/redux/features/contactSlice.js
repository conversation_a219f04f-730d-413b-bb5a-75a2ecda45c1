import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { api } from "../../axios/axiosConfig";

// Async thunk to call the import-contacts API
export const postImportedContacts = createAsyncThunk(
  "import-contacts",
  async (payload) => {
    try {
      const response = await api.post("import-contacts", payload);
      return response;
    } catch (error) {
      return error;
    }
  }
);
export const getContacts = createAsyncThunk("contact/list", async (payload) => {
  try {
    // Construct params properly - merge payload with default pagination
    const defaultParams = { page: 1, limit: 700 };
    const params = payload ? { ...defaultParams, ...payload } : defaultParams;
    const response = await api.get("contact/list", { params });
    return response?.data;
  } catch (error) {
    return error;
  }
});

export const getDuplicates = createAsyncThunk(
  "contacts/check-duplicates",
  async (payload) => {
    try {
      const response = await api.get("contacts/check-duplicates", payload);
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

export const getContactDetails = createAsyncThunk(
  "contact/details",
  async (payload) => {
    try {
      const response = await api.get(`/contact/${payload.id}/details`);
      return response?.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const mergeContacts = createAsyncThunk(
  "contacts/merge",
  async (payload) => {
    try {
      const response = await api.put("contacts/merge", payload);
      return response.data;
    } catch (error) {
      console.error("Error merging contacts:", error);
      throw error;
    }
  }
);
export const mergeAllContacts = createAsyncThunk(
  "contacts/mergeAll",
  async (payload) => {
    try {
      const response = await api.put("/contacts/all-merge", payload);
      return response.data;
    } catch (error) {
      console.error("Error merging contacts:", error);
      throw error;
    }
  }
);
export const addtoFavorites = createAsyncThunk(
  "contact/add-to-favorites",
  async (payload) => {
    try {
      const response = await api.put(`/favorite/${payload.id}`, payload.data);
      return response?.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const deleteContact = createAsyncThunk(
  "contact/delete",
  async (payload) => {
    try {
      const response = await api.delete(
        `/contacts/${payload.id}`,
        payload.data
      );
      return response?.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const updateContact = createAsyncThunk(
  "contact/update",
  async (payload) => {
    try {
      console.log("Payload in updateContact:", payload);

      const response = await api.put(`/contact/${payload.id}`, payload.data);
      return response?.data;
    } catch (error) {
      return error.response;
    }
  }
);

export const getProfileContacts = createAsyncThunk(
  "contact/profile/list",
  async (payload) => {
    try {
      // Construct params properly - merge payload with default pagination
      const defaultParams = { page: 1, limit: 700 };
      const params = payload ? { ...defaultParams, ...payload } : defaultParams;
      const response = await api.get("contact/profile/list", { params });
      return response?.data;
    } catch (error) {
      return error;
    }
  }
);

export const addManualContact = createAsyncThunk(
  "contact/add-manual-contact",
  async (payload) => {
    try {
      const response = await api.post("single-contact", payload);
      return response?.data;
    } catch (error) {
      return error.response;
    }
  }
);

const createAsyncReducers = (builder, asyncThunk, stateKey) => {
  builder
    .addCase(asyncThunk.pending, (state) => {
      state[stateKey] = { loading: true, error: null, data: null };
    })
    .addCase(asyncThunk.fulfilled, (state, action) => {
      state[stateKey] = { loading: false, error: null, data: action.payload };
    })
    .addCase(asyncThunk.rejected, (state, action) => {
      state[stateKey] = { loading: false, error: action.error, data: null };
    });
};
const contactSlice = createSlice({
  name: "contactSlice",
  initialState: {
    postImportedContacts: { loading: false, error: null, data: null },
    getContacts: { loading: false, error: null, data: null },
    getProfileContacts: { loading: false, error: null, data: null },
    getDuplicatesState: { loading: false, error: null, data: null },
    mergeContactsState: { loading: false, error: null, data: null },
    mergeAllContacts: { loading: false, error: null, data: null },
  },
  reducers: {},
  extraReducers: (builder) => {
    createAsyncReducers(builder, postImportedContacts, "postImportedContacts");
    createAsyncReducers(builder, getContacts, "getContacts");
    createAsyncReducers(builder, getProfileContacts, "getProfileContacts");
    createAsyncReducers(builder, getDuplicates, "getDuplicatesState");
    createAsyncReducers(builder, mergeContacts, "mergeContactsState");
    createAsyncReducers(builder, mergeAllContacts, "mergeAllContacts");
    // createAsyncReducers(builder, fetchUserData, "userData");
    // Add more API calls dynamically
  },
});

export default contactSlice.reducer;
